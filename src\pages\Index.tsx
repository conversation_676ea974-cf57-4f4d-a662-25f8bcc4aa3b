
import { useState, useEffect } from "react";
import Layout from "@/components/layout/Layout";
import HeroSection from "@/components/home/<USER>";
import FeaturedProducts from "@/components/home/<USER>";
import CategorySection from "@/components/home/<USER>";
import { Testimonial } from "@/data/testimonials";
import { dataService } from "@/lib/dataService";
import SEO from "@/components/seo/SEO";
import StructuredData from "@/components/seo/StructuredData";

const Index = () => {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadTestimonials = async () => {
      try {
        setLoading(true);
        const featuredTestimonials = await dataService.testimonials.getFeatured(3);
        setTestimonials(featuredTestimonials);
      } catch (error) {
        console.error('Failed to load testimonials:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTestimonials();
  }, []);

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={i < rating ? 'text-yellow-400' : 'text-gray-300'}>
        ★
      </span>
    ));
  };

  return (
    <Layout>
      <SEO
        title="Dreamy Duffel - Premium Accessories & Lifestyle Products | Custom Designs India"
        description="Discover premium handbags, caps, hair bands, earrings, necklaces, and silk pillow covers at Dreamy Duffel. Including customized designs tailored to your style. Shop quality accessories in India with fast delivery."
        keywords="handbags, caps, hair bands, earrings, necklaces, silk pillow covers, custom designs, premium accessories, Dreamy Duffel, India, fashion accessories, jewelry, lifestyle products"
        url="https://dreamyduffles.shop/"
      />
      <StructuredData type="organization" />
      <StructuredData type="website" />
      <HeroSection />
      <CategorySection />
      <FeaturedProducts />

      {/* Testimonials Section */}
      <section className="py-16 bg-brand-light">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="font-serif text-3xl md:text-4xl font-bold text-brand-charcoal mb-4">
              What Our Customers Say
            </h2>
            <p className="max-w-2xl mx-auto text-gray-600">
              Read about experiences from our satisfied customers.
            </p>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-gold"></div>
              <span className="ml-3 text-gray-600">Loading testimonials...</span>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {testimonials.map((testimonial) => (
                <div key={testimonial.id} className="bg-white p-6 rounded-lg shadow-md">
                  <div className="flex items-center mb-4">
                    {testimonial.avatar ? (
                      <img
                        src={testimonial.avatar}
                        alt={testimonial.name}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-brand-gold rounded-full flex items-center justify-center text-white font-bold text-xl">
                        {testimonial.name.charAt(0).toUpperCase()}
                      </div>
                    )}
                    <div className="ml-4">
                      <h4 className="font-bold">{testimonial.name}</h4>
                      {testimonial.location && (
                        <p className="text-sm text-gray-500">{testimonial.location}</p>
                      )}
                      <div className="flex">
                        {renderStars(testimonial.rating)}
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 italic">
                    "{testimonial.content}"
                  </p>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 bg-brand-charcoal text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="font-serif text-3xl font-bold mb-4">
              Stay Updated
            </h2>
            <p className="mb-6">
              Subscribe to our newsletter for exclusive offers, new arrivals, and more.
            </p>
            <div className="flex flex-col sm:flex-row gap-2">
              <input
                type="email"
                placeholder="Your email address"
                className="flex-grow px-4 py-2 bg-white text-brand-charcoal rounded-md focus:outline-none"
              />
              <button className="bg-brand-gold hover:opacity-90 transition-opacity px-6 py-2 rounded-md">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default Index;
