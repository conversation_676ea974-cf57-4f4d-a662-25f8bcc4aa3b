export interface Testimonial {
  id: string;
  name: string;
  rating: number;
  content: string;
  avatar?: string;
  location?: string;
  productCategory?: string;
  isPublished: boolean;
  createdAt: string;
  updatedAt: string;
}

export const testimonials: Testimonial[] = [
  {
    id: "testimonial-1",
    name: "<PERSON>",
    rating: 5,
    content: "I ordered a custom handbag for my sister's birthday and it exceeded all my expectations. The quality is exceptional and the personalization was perfect!",
    location: "Mumbai, India",
    productCategory: "handbags",
    isPublished: true,
    createdAt: "2024-12-10",
    updatedAt: "2024-12-10"
  },
  {
    id: "testimonial-2",
    name: "<PERSON>",
    rating: 5,
    content: "The customized cap I received was exactly what I wanted. Great quality, fast shipping, and amazing customer service. Will definitely order again!",
    location: "Delhi, India",
    productCategory: "caps",
    isPublished: true,
    createdAt: "2024-12-08",
    updatedAt: "2024-12-08"
  },
  {
    id: "testimonial-3",
    name: "<PERSON>",
    rating: 5,
    content: "I've purchased several handbags from Dreamy Duffel and each one has been beautifully crafted. The attention to detail is remarkable. Highly recommend!",
    location: "Bangalore, India",
    productCategory: "handbags",
    isPublished: true,
    createdAt: "2024-12-05",
    updatedAt: "2024-12-05"
  },
  {
    id: "testimonial-4",
    name: "Priya Sharma",
    rating: 4,
    content: "Beautiful earrings with excellent craftsmanship. The packaging was also very elegant. Perfect for gifting!",
    location: "Chennai, India",
    productCategory: "earrings",
    isPublished: true,
    createdAt: "2024-12-03",
    updatedAt: "2024-12-03"
  },
  {
    id: "testimonial-5",
    name: "Rajesh Kumar",
    rating: 5,
    content: "The custom pillow covers turned out amazing! The embroidery work is top-notch and the fabric quality is excellent.",
    location: "Pune, India",
    productCategory: "pillowcovers",
    isPublished: true,
    createdAt: "2024-12-01",
    updatedAt: "2024-12-01"
  },
  {
    id: "testimonial-6",
    name: "Anita Desai",
    rating: 5,
    content: "Love the hairbands! They're stylish, comfortable, and well-made. Great addition to my accessory collection.",
    location: "Kolkata, India",
    productCategory: "hairbands",
    isPublished: true,
    createdAt: "2024-11-28",
    updatedAt: "2024-11-28"
  }
];

export const getTestimonials = (): Testimonial[] => {
  return testimonials.filter(testimonial => testimonial.isPublished);
};

export const getTestimonialsByCategory = (category: string): Testimonial[] => {
  if (category === "all") {
    return getTestimonials();
  }
  return testimonials.filter(
    testimonial => testimonial.isPublished && testimonial.productCategory === category
  );
};

export const getTestimonialById = (id: string): Testimonial | undefined => {
  return testimonials.find(testimonial => testimonial.id === id && testimonial.isPublished);
};

export const getFeaturedTestimonials = (limit: number = 3): Testimonial[] => {
  return getTestimonials()
    .sort((a, b) => b.rating - a.rating)
    .slice(0, limit);
};

export const getTestimonialsByRating = (rating: number): Testimonial[] => {
  return testimonials.filter(
    testimonial => testimonial.isPublished && testimonial.rating === rating
  );
};
