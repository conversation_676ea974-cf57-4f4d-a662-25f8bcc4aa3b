import { Metadata } from 'next';
import Layout from "@/components/layout/Layout";
import HeroSection from "@/components/home/<USER>";
import FeaturedProducts from "@/components/home/<USER>";
import CategorySection from "@/components/home/<USER>";
import TestimonialsSection from "@/components/home/<USER>";
import NewsletterSection from "@/components/home/<USER>";
import StructuredData from "@/components/seo/StructuredData";

export const metadata: Metadata = {
  title: 'Dreamy Duffel - Premium Accessories & Lifestyle Products | Custom Designs Nepal',
  description: 'Discover premium handbags, caps, hair bands, earrings, necklaces, and silk pillow covers at Dreamy Duffel. Including customized designs tailored to your style. Shop quality accessories in Nepal with fast delivery.',
  keywords: 'handbags, caps, hair bands, earrings, necklaces, silk pillow covers, custom designs, premium accessories, Dreamy Duffel, Nepal, fashion accessories, jewelry, lifestyle products',
  openGraph: {
    title: 'Dreamy Duffel - Premium Accessories & Lifestyle Products | Custom Designs Nepal',
    description: 'Discover premium handbags, caps, hair bands, earrings, necklaces, and silk pillow covers at Dreamy Duffel. Including customized designs tailored to your style. Shop quality accessories in Nepal with fast delivery.',
    url: 'https://dreamyduffles.shop/',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Dreamy Duffel - Premium Accessories',
      },
    ],
  },
  twitter: {
    title: 'Dreamy Duffel - Premium Accessories & Lifestyle Products | Custom Designs Nepal',
    description: 'Discover premium handbags, caps, hair bands, earrings, necklaces, and silk pillow covers at Dreamy Duffel. Including customized designs tailored to your style. Shop quality accessories in Nepal with fast delivery.',
    images: ['/og-image.jpg'],
  },
  alternates: {
    canonical: 'https://dreamyduffles.shop/',
  },
};

export default function HomePage() {
  return (
    <Layout>
      <StructuredData type="organization" />
      <StructuredData type="website" />
      <HeroSection />
      <CategorySection />
      <FeaturedProducts />
      <TestimonialsSection />
      <NewsletterSection />
    </Layout>
  );
}
