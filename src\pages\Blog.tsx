import { useState, useEffect } from "react";
import Layout from "@/components/layout/Layout";
import { BlogPost } from "@/data/blog";
import { dataService } from "@/lib/dataService";
import { Link } from "react-router-dom";
import { Calendar, Clock, User, Tag } from "lucide-react";
import SEO from "@/components/seo/SEO";
import StructuredData from "@/components/seo/StructuredData";

const Blog = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [allPosts, setAllPosts] = useState<BlogPost[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const filteredPosts = selectedCategory === "all"
    ? allPosts
    : allPosts.filter(post => post.category === selectedCategory);

  useEffect(() => {
    const loadBlogData = async () => {
      try {
        setLoading(true);
        setError(null);

        const [posts, blogCategories] = await Promise.all([
          dataService.blog.getPublished(),
          dataService.blog.getCategories()
        ]);

        setAllPosts(posts);
        setCategories(blogCategories);
      } catch (err) {
        console.error('Failed to load blog data:', err);
        setError('Failed to load blog posts');
      } finally {
        setLoading(false);
      }
    };

    loadBlogData();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Layout>
      <SEO 
        title="Blog - Fashion Tips & Style Guides | Dreamy Duffel"
        description="Discover the latest fashion tips, style guides, and accessory trends on the Dreamy Duffel blog. Learn about handbag care, custom designs, and sustainable fashion."
        keywords="fashion blog, style tips, handbag care, custom accessories, sustainable fashion, Dreamy Duffel blog"
        url="https://dreamyduffles.shop/blog"
      />
      <StructuredData type="website" />
      
      <div className="py-12 md:py-20 bg-brand-cream">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h1 className="font-serif text-4xl md:text-5xl font-bold text-brand-charcoal mb-4">
              Style & Stories
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Discover fashion tips, care guides, and the stories behind our beautiful accessories.
            </p>
          </div>
        </div>
      </div>

      <div className="py-12">
        <div className="container mx-auto px-4">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-gold"></div>
              <span className="ml-3 text-gray-600">Loading blog posts...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-600 mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="btn-primary"
              >
                Try Again
              </button>
            </div>
          ) : (
            <>
              {/* Category Filter */}
              <div className="mb-8">
                <div className="flex flex-wrap gap-2 justify-center">
                  <button
                    onClick={() => setSelectedCategory("all")}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                      selectedCategory === "all"
                        ? "bg-brand-gold text-white"
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    }`}
                  >
                    All Posts
                  </button>
                  {categories.map((category) => (
                    <button
                      key={category}
                      onClick={() => setSelectedCategory(category)}
                      className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                        selectedCategory === category
                          ? "bg-brand-gold text-white"
                          : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                      }`}
                    >
                      {category}
                    </button>
                  ))}
                </div>
              </div>

          {/* Blog Posts Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredPosts.map((post) => (
              <article key={post.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <Link to={`/blog/${post.slug}`}>
                  <img
                    src={post.featuredImage}
                    alt={`${post.title} - Dreamy Duffel Blog`}
                    className="w-full h-48 object-cover hover:scale-105 transition-transform duration-300"
                  />
                </Link>
                <div className="p-6">
                  <div className="flex items-center text-sm text-gray-500 mb-3">
                    <Tag size={14} className="mr-1" />
                    <span className="mr-4">{post.category}</span>
                    <Clock size={14} className="mr-1" />
                    <span>{post.readTime} min read</span>
                  </div>
                  
                  <h2 className="font-serif text-xl font-bold text-brand-charcoal mb-3 hover:text-brand-gold transition-colors">
                    <Link to={`/blog/${post.slug}`}>
                      {post.title}
                    </Link>
                  </h2>
                  
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>
                  
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center">
                      <User size={14} className="mr-1" />
                      <span>{post.author}</span>
                    </div>
                    <div className="flex items-center">
                      <Calendar size={14} className="mr-1" />
                      <span>{formatDate(post.publishedAt)}</span>
                    </div>
                  </div>
                  
                  <Link
                    to={`/blog/${post.slug}`}
                    className="inline-block mt-4 text-brand-gold hover:text-brand-charcoal font-medium transition-colors"
                  >
                    Read More →
                  </Link>
                </div>
              </article>
            ))}
          </div>

              {filteredPosts.length === 0 && (
                <div className="text-center py-12">
                  <p className="text-gray-600 mb-4">No posts found in this category.</p>
                  <button
                    onClick={() => setSelectedCategory("all")}
                    className="btn-primary"
                  >
                    View All Posts
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Blog;
