import { Helmet } from 'react-helmet-async';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  article?: {
    publishedTime?: string;
    modifiedTime?: string;
    author?: string;
    section?: string;
    tags?: string[];
  };
  product?: {
    price?: number;
    currency?: string;
    availability?: string;
    condition?: string;
    brand?: string;
    category?: string;
  };
}

const SEO = ({
  title = "Dreamy Duffel - Premium Handbags & Caps | Custom Designs India",
  description = "Discover premium handbags and caps at Dreamy Duffel, including customized designs tailored to your style. Shop quality accessories in India with fast delivery.",
  keywords = "handbags, caps, custom designs, premium accessories, Dreamy Duffel, India, fashion accessories",
  image = "https://dreamyduffles.shop/og-image.jpg",
  url = "https://dreamyduffles.shop/",
  type = "website",
  article,
  product,
}: SEOProps) => {
  const siteTitle = "Dreamy Duffel";
  const fullTitle = title.includes(siteTitle) ? title : `${title} | ${siteTitle}`;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="robots" content="index, follow" />
      <link rel="canonical" href={url} />

      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={url} />
      <meta property="og:image" content={image} />
      <meta property="og:site_name" content={siteTitle} />
      <meta property="og:locale" content="en_IN" />

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@dreamyduffel" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />

      {/* Article specific meta tags */}
      {article && (
        <>
          <meta property="article:published_time" content={article.publishedTime} />
          <meta property="article:modified_time" content={article.modifiedTime} />
          <meta property="article:author" content={article.author} />
          <meta property="article:section" content={article.section} />
          {article.tags?.map((tag, index) => (
            <meta key={index} property="article:tag" content={tag} />
          ))}
        </>
      )}

      {/* Product specific meta tags */}
      {product && (
        <>
          <meta property="product:price:amount" content={product.price?.toString()} />
          <meta property="product:price:currency" content={product.currency || "INR"} />
          <meta property="product:availability" content={product.availability || "in stock"} />
          <meta property="product:condition" content={product.condition || "new"} />
          <meta property="product:brand" content={product.brand || "Dreamy Duffel"} />
          <meta property="product:category" content={product.category} />
        </>
      )}

      {/* Additional SEO Meta Tags */}
      <meta name="author" content="Dreamy Duffel" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Language" content="en" />
      <meta name="geo.region" content="IN" />
      <meta name="geo.country" content="India" />
    </Helmet>
  );
};

export default SEO;
