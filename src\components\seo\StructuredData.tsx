import { Product } from '@/data/products';

interface OrganizationSchema {
  "@context": string;
  "@type": string;
  name: string;
  url: string;
  logo: string;
  description: string;
  address: {
    "@type": string;
    addressCountry: string;
  };
  contactPoint: {
    "@type": string;
    contactType: string;
    email: string;
  };
  sameAs: string[];
}

interface WebsiteSchema {
  "@context": string;
  "@type": string;
  name: string;
  url: string;
  description: string;
  publisher: {
    "@type": string;
    name: string;
  };
}

interface ProductSchema {
  "@context": string;
  "@type": string;
  name: string;
  description: string;
  image: string[];
  brand: {
    "@type": string;
    name: string;
  };
  offers: {
    "@type": string;
    price: number;
    priceCurrency: string;
    availability: string;
    url: string;
  };
  category: string;
}

interface BreadcrumbSchema {
  "@context": string;
  "@type": string;
  itemListElement: Array<{
    "@type": string;
    position: number;
    name: string;
    item: string;
  }>;
}

interface StructuredDataProps {
  type: 'organization' | 'website' | 'product' | 'breadcrumb' | 'article';
  data?: Record<string, unknown>;
  product?: Product;
  breadcrumbs?: Array<{ name: string; url: string }>;
}

const StructuredData = ({ type, data, product, breadcrumbs }: StructuredDataProps) => {
  const getOrganizationSchema = (): OrganizationSchema => ({
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "Dreamy Duffel",
    url: "https://dreamyduffles.shop",
    logo: "https://dreamyduffles.shop/logo.png",
    description: "Premium handbags and caps with custom designs in India",
    address: {
      "@type": "PostalAddress",
      addressCountry: "IN"
    },
    contactPoint: {
      "@type": "ContactPoint",
      contactType: "customer service",
      email: "<EMAIL>"
    },
    sameAs: [
      "https://www.facebook.com/dreamyduffel",
      "https://www.instagram.com/dreamyduffel",
      "https://twitter.com/dreamyduffel"
    ]
  });

  const getWebsiteSchema = (): WebsiteSchema => ({
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "Dreamy Duffel",
    url: "https://dreamyduffles.shop",
    description: "Premium handbags and caps with custom designs in India",
    publisher: {
      "@type": "Organization",
      name: "Dreamy Duffel"
    }
  });

  const getProductSchema = (product: Product): ProductSchema => ({
    "@context": "https://schema.org",
    "@type": "Product",
    name: product.name,
    description: product.description,
    image: product.images,
    brand: {
      "@type": "Brand",
      name: "Dreamy Duffel"
    },
    offers: {
      "@type": "Offer",
      price: product.price,
      priceCurrency: "INR",
      availability: product.inStock ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
      url: `https://dreamyduffles.shop/products/${product.id}`
    },
    category: product.category
  });

  const getBreadcrumbSchema = (breadcrumbs: Array<{ name: string; url: string }>): BreadcrumbSchema => ({
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: crumb.name,
      item: crumb.url
    }))
  });

  let schema: Record<string, unknown> | OrganizationSchema | WebsiteSchema | ProductSchema | BreadcrumbSchema;

  switch (type) {
    case 'organization':
      schema = getOrganizationSchema();
      break;
    case 'website':
      schema = getWebsiteSchema();
      break;
    case 'product':
      if (product) {
        schema = getProductSchema(product);
      } else if (data) {
        schema = {
          "@context": "https://schema.org",
          "@type": "Product",
          name: data.name,
          description: data.description,
          image: data.image,
          brand: {
            "@type": "Brand",
            name: data.brand || "Dreamy Duffel"
          },
          offers: {
            "@type": "Offer",
            price: data.price,
            priceCurrency: data.currency || "INR",
            availability: data.availability === "in stock" ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
            url: data.url
          },
          category: data.category,
          sku: data.sku
        };
      }
      break;
    case 'article':
      if (data) {
        schema = {
          "@context": "https://schema.org",
          "@type": "Article",
          headline: data.headline,
          description: data.description,
          image: data.image,
          author: {
            "@type": "Person",
            name: data.author
          },
          publisher: {
            "@type": "Organization",
            name: "Dreamy Duffel",
            logo: {
              "@type": "ImageObject",
              url: "https://dreamyduffles.shop/logo.png"
            }
          },
          datePublished: data.publishedTime,
          dateModified: data.modifiedTime,
          articleSection: data.section,
          keywords: Array.isArray(data.tags) ? data.tags.join(', ') : undefined,
          url: data.url
        };
      }
      break;
    case 'breadcrumb':
      if (breadcrumbs) {
        schema = getBreadcrumbSchema(breadcrumbs);
      }
      break;
    default:
      return null;
  }

  if (!schema) return null;

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
};

export default StructuredData;
