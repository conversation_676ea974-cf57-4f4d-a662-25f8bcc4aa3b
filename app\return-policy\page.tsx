import { Metadata } from 'next';
import Layout from "@/components/layout/Layout";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

export const metadata: Metadata = {
  title: "Return & Cancellation Policy | Dreamy Duffel",
  description: "Learn about Dreamy Duffel's return and cancellation policy. We offer hassle-free returns within 30 days and flexible cancellation options for your peace of mind.",
  keywords: "return policy, cancellation policy, Dreamy Duffel, refund, exchange, customer service",
  openGraph: {
    title: "Return & Cancellation Policy | Dreamy Duffel",
    description: "Learn about Dreamy Duffel's return and cancellation policy. We offer hassle-free returns within 30 days and flexible cancellation options for your peace of mind.",
    url: "https://dreamyduffles.shop/return-policy",
  },
  alternates: {
    canonical: "https://dreamyduffles.shop/return-policy",
  },
};

export default function ReturnPolicyPage() {
  return (
    <Layout>
      <div className="py-12 md:py-20 bg-brand-cream">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h1 className="font-serif text-4xl md:text-5xl font-bold text-brand-charcoal mb-4">
              Return & Cancellation Policy
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We want you to be completely satisfied with your purchase. Please review our policies below.
            </p>
          </div>
        </div>
      </div>

      <div className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-12">
              <h2 className="font-serif text-2xl font-bold text-brand-charcoal mb-4">
                Returns & Exchanges
              </h2>
              <p className="text-gray-600 mb-6">
                At Dreamy Duffel, we stand behind the quality of our products. If for any reason you are not completely satisfied with your purchase, we offer a straightforward return policy.
              </p>

              <div className="bg-brand-cream p-6 rounded-lg mb-8">
                <h3 className="font-medium text-brand-charcoal mb-3">Standard Products</h3>
                <ul className="space-y-2 text-gray-600 list-disc list-inside">
                  <li>Returns are accepted within 30 days of delivery.</li>
                  <li>Item must be unused, unworn, and in the original packaging with all tags attached.</li>
                  <li>Return shipping costs are the responsibility of the customer unless the item is defective or we made an error.</li>
                  <li>Once your return is received and inspected, we will process your refund within 5-7 business days.</li>
                  <li>Refunds will be issued to the original payment method.</li>
                </ul>
              </div>

              <div className="bg-brand-cream p-6 rounded-lg">
                <h3 className="font-medium text-brand-charcoal mb-3">Custom/Personalized Products</h3>
                <ul className="space-y-2 text-gray-600 list-disc list-inside">
                  <li>Custom and personalized items are generally non-returnable unless there is a manufacturing defect.</li>
                  <li>If you receive a defective custom item, please contact us within 7 days of delivery.</li>
                  <li>We will work with you to resolve the issue through replacement or refund.</li>
                </ul>
              </div>
            </div>

            <div className="mb-12">
              <h2 className="font-serif text-2xl font-bold text-brand-charcoal mb-6">
                Frequently Asked Questions
              </h2>
              
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="item-1">
                  <AccordionTrigger>How do I initiate a return?</AccordionTrigger>
                  <AccordionContent>
                    To initiate a return, please contact our customer service <NAME_EMAIL> or call us at +91-XXXXXXXXX. We'll provide you with a return authorization number and detailed instructions.
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="item-2">
                  <AccordionTrigger>What if my item arrives damaged?</AccordionTrigger>
                  <AccordionContent>
                    If your item arrives damaged, please contact us immediately with photos of the damage. We'll arrange for a replacement or full refund at no cost to you, including return shipping.
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="item-3">
                  <AccordionTrigger>Can I exchange an item for a different size or color?</AccordionTrigger>
                  <AccordionContent>
                    Yes, exchanges are possible within 30 days of delivery, subject to availability. The item must be in its original condition. Exchange shipping costs may apply unless the exchange is due to our error.
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="item-4">
                  <AccordionTrigger>How long does it take to process a refund?</AccordionTrigger>
                  <AccordionContent>
                    Once we receive and inspect your returned item, we'll process your refund within 5-7 business days. The refund will appear in your original payment method within 3-10 business days, depending on your bank or card issuer.
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="item-5">
                  <AccordionTrigger>What items cannot be returned?</AccordionTrigger>
                  <AccordionContent>
                    The following items cannot be returned: personalized/custom items (unless defective), items that have been worn or used, items without original tags or packaging, and items returned after 30 days.
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="item-6">
                  <AccordionTrigger>Can I cancel my order?</AccordionTrigger>
                  <AccordionContent>
                    Orders can be cancelled within 24 hours of placement, provided they haven't entered production. For custom items, cancellation may not be possible once production has begun. Please contact us as soon as possible if you need to cancel.
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>

            <div className="bg-brand-light p-8 rounded-lg">
              <h2 className="font-serif text-2xl font-bold text-brand-charcoal mb-4">
                Need Help?
              </h2>
              <p className="text-gray-600 mb-6">
                Our customer service team is here to help with any questions about returns, exchanges, or your order.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-medium text-brand-charcoal mb-2">Email Support</h3>
                  <p className="text-gray-600"><EMAIL></p>
                  <p className="text-sm text-gray-500">Response within 24 hours</p>
                </div>
                <div>
                  <h3 className="font-medium text-brand-charcoal mb-2">Phone Support</h3>
                  <p className="text-gray-600">+91-XXXXXXXXX</p>
                  <p className="text-sm text-gray-500">Mon-Fri, 9 AM - 6 PM IST</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
