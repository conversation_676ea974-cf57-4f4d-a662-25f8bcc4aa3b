import { Product } from '@/data/products'
import { BlogPost } from '@/data/blog'
import { Testimonial } from '@/data/testimonials'

// CMS API Configuration
const CMS_API_URL = import.meta.env.VITE_CMS_API_URL || 'http://localhost:3001/api'

// API Response Types
interface ApiResponse<T> {
  data?: T
  products?: T[]
  posts?: T[]
  testimonials?: T[]
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
  error?: string
}

// Error handling utility
class ApiError extends Error {
  constructor(message: string, public status?: number) {
    super(message)
    this.name = 'ApiError'
  }
}

// Generic fetch wrapper with error handling
async function apiRequest<T>(endpoint: string, options?: RequestInit): Promise<T> {
  try {
    const url = `${CMS_API_URL}${endpoint}`
    console.log(`🌐 API Request: ${url}`)
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    })

    if (!response.ok) {
      throw new ApiError(
        `API request failed: ${response.status} ${response.statusText}`,
        response.status
      )
    }

    const data = await response.json()
    console.log(`✅ API Response: ${endpoint}`, data)
    return data
  } catch (error) {
    console.error(`❌ API Error: ${endpoint}`, error)
    
    if (error instanceof ApiError) {
      throw error
    }
    
    throw new ApiError(
      error instanceof Error ? error.message : 'Unknown API error'
    )
  }
}

// Products API
export const productsApi = {
  // Get all products with optional filtering
  async getAll(params?: {
    category?: string
    featured?: boolean
    inStock?: boolean
    page?: number
    limit?: number
    search?: string
  }): Promise<{ products: Product[]; pagination?: any }> {
    const searchParams = new URLSearchParams()
    
    if (params?.category && params.category !== 'all') {
      searchParams.append('category', params.category)
    }
    if (params?.featured !== undefined) {
      searchParams.append('featured', params.featured.toString())
    }
    if (params?.inStock !== undefined) {
      searchParams.append('inStock', params.inStock.toString())
    }
    if (params?.page) {
      searchParams.append('page', params.page.toString())
    }
    if (params?.limit) {
      searchParams.append('limit', params.limit.toString())
    }
    if (params?.search) {
      searchParams.append('search', params.search)
    }

    const query = searchParams.toString()
    const endpoint = `/products${query ? `?${query}` : ''}`
    
    return apiRequest<{ products: Product[]; pagination?: any }>(endpoint)
  },

  // Get featured products
  async getFeatured(): Promise<Product[]> {
    const response = await this.getAll({ featured: true })
    return response.products
  },

  // Get products by category
  async getByCategory(category: string): Promise<Product[]> {
    const response = await this.getAll({ category })
    return response.products
  },

  // Get single product by ID
  async getById(id: string): Promise<Product> {
    return apiRequest<Product>(`/products/${id}`)
  },
}

// Blog API
export const blogApi = {
  // Get all blog posts with optional filtering
  async getAll(params?: {
    category?: string
    published?: boolean
    page?: number
    limit?: number
    search?: string
  }): Promise<{ posts: BlogPost[]; pagination?: any }> {
    const searchParams = new URLSearchParams()
    
    // Only fetch published posts for static website
    searchParams.append('published', 'true')
    
    if (params?.category) {
      searchParams.append('category', params.category)
    }
    if (params?.page) {
      searchParams.append('page', params.page.toString())
    }
    if (params?.limit) {
      searchParams.append('limit', params.limit.toString())
    }
    if (params?.search) {
      searchParams.append('search', params.search)
    }

    const query = searchParams.toString()
    const endpoint = `/blog?${query}`
    
    return apiRequest<{ posts: BlogPost[]; pagination?: any }>(endpoint)
  },

  // Get published blog posts
  async getPublished(): Promise<BlogPost[]> {
    const response = await this.getAll({ published: true })
    return response.posts
  },

  // Get blog posts by category
  async getByCategory(category: string): Promise<BlogPost[]> {
    const response = await this.getAll({ category, published: true })
    return response.posts
  },

  // Get single blog post by slug or ID
  async getBySlug(slug: string): Promise<BlogPost> {
    return apiRequest<BlogPost>(`/blog/${slug}`)
  },

  // Get blog categories
  async getCategories(): Promise<string[]> {
    const response = await this.getAll()
    const categories = response.posts.map(post => post.category)
    return [...new Set(categories)]
  },
}

// Cache utilities for better performance
class ApiCache {
  private cache = new Map<string, { data: any; timestamp: number }>()
  private readonly TTL = 5 * 60 * 1000 // 5 minutes

  set(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() })
  }

  get(key: string): any | null {
    const cached = this.cache.get(key)
    if (!cached) return null

    if (Date.now() - cached.timestamp > this.TTL) {
      this.cache.delete(key)
      return null
    }

    return cached.data
  }

  clear(): void {
    this.cache.clear()
  }
}

export const apiCache = new ApiCache()

// Cached API functions
export const cachedProductsApi = {
  async getFeatured(): Promise<Product[]> {
    const cacheKey = 'featured-products'
    const cached = apiCache.get(cacheKey)
    if (cached) return cached

    const products = await productsApi.getFeatured()
    apiCache.set(cacheKey, products)
    return products
  },

  async getByCategory(category: string): Promise<Product[]> {
    const cacheKey = `products-${category}`
    const cached = apiCache.get(cacheKey)
    if (cached) return cached

    const products = await productsApi.getByCategory(category)
    apiCache.set(cacheKey, products)
    return products
  },
}

// Testimonials API
export const testimonialsApi = {
  // Get all testimonials with optional filtering
  async getAll(params?: {
    category?: string
    rating?: number
    published?: boolean
    page?: number
    limit?: number
    search?: string
  }): Promise<{ testimonials: Testimonial[]; pagination?: any }> {
    const searchParams = new URLSearchParams()

    // Only fetch published testimonials for static website
    searchParams.append('published', 'true')

    if (params?.category && params.category !== 'all') {
      searchParams.append('category', params.category)
    }
    if (params?.rating !== undefined) {
      searchParams.append('rating', params.rating.toString())
    }
    if (params?.page) {
      searchParams.append('page', params.page.toString())
    }
    if (params?.limit) {
      searchParams.append('limit', params.limit.toString())
    }
    if (params?.search) {
      searchParams.append('search', params.search)
    }

    const query = searchParams.toString()
    const endpoint = `/testimonials?${query}`

    return apiRequest<{ testimonials: Testimonial[]; pagination?: any }>(endpoint)
  },

  // Get published testimonials
  async getPublished(): Promise<Testimonial[]> {
    const response = await this.getAll({ published: true })
    return response.testimonials
  },

  // Get testimonials by category
  async getByCategory(category: string): Promise<Testimonial[]> {
    const response = await this.getAll({ category, published: true })
    return response.testimonials
  },

  // Get testimonials by rating
  async getByRating(rating: number): Promise<Testimonial[]> {
    const response = await this.getAll({ rating, published: true })
    return response.testimonials
  },

  // Get featured testimonials (highest rated)
  async getFeatured(limit: number = 3): Promise<Testimonial[]> {
    const response = await this.getAll({ limit, published: true })
    return response.testimonials.sort((a, b) => b.rating - a.rating).slice(0, limit)
  },

  // Get single testimonial by ID
  async getById(id: string): Promise<Testimonial> {
    return apiRequest<Testimonial>(`/testimonials/${id}`)
  },
}

export const cachedBlogApi = {
  async getPublished(): Promise<BlogPost[]> {
    const cacheKey = 'published-posts'
    const cached = apiCache.get(cacheKey)
    if (cached) return cached

    const posts = await blogApi.getPublished()
    apiCache.set(cacheKey, posts)
    return posts
  },

  async getCategories(): Promise<string[]> {
    const cacheKey = 'blog-categories'
    const cached = apiCache.get(cacheKey)
    if (cached) return cached

    const categories = await blogApi.getCategories()
    apiCache.set(cacheKey, categories)
    return categories
  },
}

export const cachedTestimonialsApi = {
  async getFeatured(limit: number = 3): Promise<Testimonial[]> {
    const cacheKey = `featured-testimonials-${limit}`
    const cached = apiCache.get(cacheKey)
    if (cached) return cached

    const testimonials = await testimonialsApi.getFeatured(limit)
    apiCache.set(cacheKey, testimonials)
    return testimonials
  },

  async getByCategory(category: string): Promise<Testimonial[]> {
    const cacheKey = `testimonials-${category}`
    const cached = apiCache.get(cacheKey)
    if (cached) return cached

    const testimonials = await testimonialsApi.getByCategory(category)
    apiCache.set(cacheKey, testimonials)
    return testimonials
  },
}

export { ApiError }
