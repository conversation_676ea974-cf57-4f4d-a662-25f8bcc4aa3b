import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import './globals.css';
import ClientProviders from './providers';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    default: 'Dreamy Duffel - Premium Handbags & Caps | Custom Designs India',
    template: '%s | Dreamy Duffel',
  },
  description: 'Discover premium handbags and caps at Dreamy Duffel, including customized designs tailored to your style. Shop quality accessories in India with fast delivery.',
  keywords: 'handbags, caps, custom designs, premium accessories, Dreamy Duffel, India, fashion accessories',
  authors: [{ name: 'Dreamy Duffel' }],
  creator: '<PERSON><PERSON> Duffel',
  publisher: 'Dreamy Duffel',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://dreamyduffles.shop'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_IN',
    url: 'https://dreamyduffles.shop',
    siteName: 'Dreamy Duffel',
    title: 'Dreamy Duffel - Premium Handbags & Caps | Custom Designs India',
    description: 'Discover premium handbags and caps at Dreamy Duffel, including customized designs tailored to your style. Shop quality accessories in India with fast delivery.',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Dreamy Duffel - Premium Accessories',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@dreamyduffel',
    creator: '@dreamyduffel',
    title: 'Dreamy Duffel - Premium Handbags & Caps | Custom Designs India',
    description: 'Discover premium handbags and caps at Dreamy Duffel, including customized designs tailored to your style. Shop quality accessories in India with fast delivery.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ClientProviders>
          <TooltipProvider>
            {children}
            <Toaster />
            <Sonner />
          </TooltipProvider>
        </ClientProviders>
      </body>
    </html>
  );
}
