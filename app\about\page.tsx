import { Metadata } from 'next';
import Layout from "@/components/layout/Layout";
import pooja from "@/assets/image/pooja.jpg";
import ashish from "@/assets/image/ashish.jpg";
import StructuredData from "@/components/seo/StructuredData";

export const metadata: Metadata = {
  title: "About Us - Our Story & Mission | Dreamy Duffel",
  description: "Learn about Dreamy Duffel's journey, our commitment to quality craftsmanship, and the passionate team behind India's premium handbags and caps.",
  keywords: "about Dreamy Duffel, our story, premium accessories India, handcrafted bags, custom designs, quality craftsmanship",
  openGraph: {
    title: "About Us - Our Story & Mission | Dreamy Duffel",
    description: "Learn about Dreamy Duffel's journey, our commitment to quality craftsmanship, and the passionate team behind India's premium handbags and caps.",
    url: "https://dreamyduffles.shop/about",
  },
  alternates: {
    canonical: "https://dreamyduffles.shop/about",
  },
};

export default function AboutPage() {
  return (
    <Layout>
      <StructuredData type="organization" />
      <div className="py-12 md:py-20 bg-brand-cream">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h1 className="font-serif text-4xl md:text-5xl font-bold text-brand-charcoal mb-4">
              About Dreamy Duffel
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Crafting quality accessories that blend style, functionality, and personalization.
            </p>
          </div>
        </div>
      </div>

      {/* Our Story Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-8 md:mb-0 md:pr-8">
              <img
                src="https://images.unsplash.com/photo-1671535108665-eeeb723ebebf?q=80&w=1470&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                alt="Our workshop"
                className="rounded-lg shadow-md w-full h-auto"
              />
            </div>
            <div className="md:w-1/2">
              <h2 className="font-serif text-3xl font-bold text-brand-charcoal mb-4">
                Our Story
              </h2>
              <p className="text-gray-600 mb-4">
                Dreamy Duffel was founded in 2025 with a simple mission: to create high-quality, stylish accessories that reflect the individuality of our customers. What began as a small workshop crafting custom handbags has grown into a beloved brand known for both our regular collections and personalized designs.
              </p>
              <p className="text-gray-600 mb-4">
                We believe that accessories are more than just practical items – they're an expression of personal style and identity. That's why we put so much care into each product we create, from selecting premium materials to ensuring precise craftsmanship.
              </p>
              <p className="text-gray-600">
                Our team of skilled artisans combines traditional techniques with modern design to create pieces that are both timeless and contemporary. Whether you choose an item from our curated collections or opt for a custom design, you can be confident that you're receiving an accessory of exceptional quality.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Our Values Section */}
      <section className="py-16 bg-brand-light">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="font-serif text-3xl font-bold text-brand-charcoal mb-4">
              Our Values
            </h2>
            <p className="text-gray-600 max-w-3xl mx-auto">
              The principles that guide everything we do at Dreamy Duffel.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-brand-gold rounded-full flex items-center justify-center text-white mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="m21 16-4 4-4-4" />
                  <path d="M17 20V4" />
                  <path d="m3 8 4-4 4 4" />
                  <path d="M7 4v16" />
                </svg>
              </div>
              <h3 className="font-serif text-xl font-bold text-brand-charcoal mb-2">Quality</h3>
              <p className="text-gray-600">
                We never compromise on materials or craftsmanship. Every stitch, seam, and detail is carefully executed to create accessories that are built to last.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-brand-gold rounded-full flex items-center justify-center text-white mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M12 17a5 5 0 1 0 0-10 5 5 0 0 0 0 10Z" />
                  <circle cx="12" cy="12" r="9" />
                </svg>
              </div>
              <h3 className="font-serif text-xl font-bold text-brand-charcoal mb-2">Sustainability</h3>
              <p className="text-gray-600">
                We're committed to responsible production practices, from sourcing materials to minimizing waste. We believe in creating beautiful products that respect our planet.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="w-16 h-16 bg-brand-gold rounded-full flex items-center justify-center text-white mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20" />
                </svg>
              </div>
              <h3 className="font-serif text-xl font-bold text-brand-charcoal mb-2">Personalization</h3>
              <p className="text-gray-600">
                We celebrate individuality. Our custom designs allow you to express your unique style through accessories that are made specifically for you.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="font-serif text-3xl font-bold text-brand-charcoal mb-4">
              Meet Our Team
            </h2>
            <p className="text-gray-600 max-w-3xl mx-auto">
              The talented individuals behind Dreamy Duffel.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="mb-4 relative inline-block">
                <img
                  src={pooja}
                  alt="Pooja poddar"
                  className="w-48 h-48 object-cover rounded-full border-4 border-brand-gold"
                />
              </div>
              <h3 className="font-serif text-xl font-bold text-brand-charcoal">Pooja Poddar</h3>
              <p className="text-brand-gold font-medium">Founder & Designer</p>
            </div>

            <div className="text-center">
              <div className="mb-4 relative inline-block">
                <img
                  src={ashish}
                  alt="Ashish Kamat"
                  className="w-48 h-48 object-cover rounded-full border-4 border-brand-gold"
                />
              </div>
              <h3 className="font-serif text-xl font-bold text-brand-charcoal">Ashish Kamat</h3>
              <p className="text-brand-gold font-medium">Co-Founder & CTO</p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-brand-cream">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="font-serif text-3xl font-bold text-brand-charcoal mb-4">
              Connect With Us
            </h2>
            <p className="text-gray-600 mb-8">
              Have questions or want to learn more about our products? We'd love to hear from you.
            </p>
            <div className="bg-white p-8 rounded-lg shadow-md">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <input
                  type="text"
                  placeholder="Your Name"
                  className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-gold"
                />
                <input
                  type="email"
                  placeholder="Your Email"
                  className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-gold"
                />
              </div>
              <textarea
                placeholder="Your Message"
                rows={5}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-gold mb-6"
              ></textarea>
              <button className="btn-primary">
                Send Message
              </button>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
