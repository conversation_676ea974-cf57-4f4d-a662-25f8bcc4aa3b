import { Product, getFeaturedProducts, getProductsByCategory, getProductById } from '@/data/products'
import { BlogPost, getBlogPosts, getBlogPostBySlug, getBlogPostsByCategory, getBlogCategories } from '@/data/blog'
import { Testimonial, getTestimonials, getTestimonialsByCategory, getTestimonialById, getFeaturedTestimonials } from '@/data/testimonials'
import { productsApi, blogApi, testimonialsApi, cachedProductsApi, cachedBlogApi, cachedTestimonialsApi, ApiError } from './api'

// Configuration
const USE_CMS_API = import.meta.env.VITE_USE_CMS_API === 'true'
const FALLBACK_TO_STATIC = import.meta.env.VITE_FALLBACK_TO_STATIC !== 'false' // Default to true

console.log('🔧 Data Service Config:', { USE_CMS_API, FALLBACK_TO_STATIC })

// Data service that handles both API and static data with fallbacks
export const dataService = {
  // Products
  products: {
    async getFeatured(): Promise<Product[]> {
      if (USE_CMS_API) {
        try {
          return await cachedProductsApi.getFeatured()
        } catch (error) {
          console.warn('⚠️ CMS API failed for featured products, falling back to static data:', error)
          if (FALLBACK_TO_STATIC) {
            return getFeaturedProducts()
          }
          throw error
        }
      }
      return getFeaturedProducts()
    },

    async getByCategory(category: string): Promise<Product[]> {
      if (USE_CMS_API) {
        try {
          return await cachedProductsApi.getByCategory(category)
        } catch (error) {
          console.warn(`⚠️ CMS API failed for products category ${category}, falling back to static data:`, error)
          if (FALLBACK_TO_STATIC) {
            return getProductsByCategory(category)
          }
          throw error
        }
      }
      return getProductsByCategory(category)
    },

    async getById(id: string): Promise<Product | undefined> {
      if (USE_CMS_API) {
        try {
          return await productsApi.getById(id)
        } catch (error) {
          console.warn(`⚠️ CMS API failed for product ${id}, falling back to static data:`, error)
          if (FALLBACK_TO_STATIC) {
            return getProductById(id)
          }
          throw error
        }
      }
      return getProductById(id)
    },

    async getAll(params?: {
      category?: string
      featured?: boolean
      inStock?: boolean
      page?: number
      limit?: number
      search?: string
    }): Promise<{ products: Product[]; pagination?: any }> {
      if (USE_CMS_API) {
        try {
          return await productsApi.getAll(params)
        } catch (error) {
          console.warn('⚠️ CMS API failed for all products, falling back to static data:', error)
          if (FALLBACK_TO_STATIC) {
            let products = getProductsByCategory(params?.category || 'all')
            
            // Apply filters
            if (params?.featured !== undefined) {
              products = products.filter(p => p.isFeatured === params.featured)
            }
            if (params?.inStock !== undefined) {
              products = products.filter(p => p.inStock === params.inStock)
            }
            if (params?.search) {
              const search = params.search.toLowerCase()
              products = products.filter(p => 
                p.name.toLowerCase().includes(search) ||
                p.description.toLowerCase().includes(search)
              )
            }

            return { products }
          }
          throw error
        }
      }
      
      // Static data implementation
      let products = getProductsByCategory(params?.category || 'all')
      
      if (params?.featured !== undefined) {
        products = products.filter(p => p.isFeatured === params.featured)
      }
      if (params?.inStock !== undefined) {
        products = products.filter(p => p.inStock === params.inStock)
      }
      if (params?.search) {
        const search = params.search.toLowerCase()
        products = products.filter(p => 
          p.name.toLowerCase().includes(search) ||
          p.description.toLowerCase().includes(search)
        )
      }

      return { products }
    },
  },

  // Blog
  blog: {
    async getPublished(): Promise<BlogPost[]> {
      if (USE_CMS_API) {
        try {
          return await cachedBlogApi.getPublished()
        } catch (error) {
          console.warn('⚠️ CMS API failed for published posts, falling back to static data:', error)
          if (FALLBACK_TO_STATIC) {
            return getBlogPosts()
          }
          throw error
        }
      }
      return getBlogPosts()
    },

    async getByCategory(category: string): Promise<BlogPost[]> {
      if (USE_CMS_API) {
        try {
          return await blogApi.getByCategory(category)
        } catch (error) {
          console.warn(`⚠️ CMS API failed for blog category ${category}, falling back to static data:`, error)
          if (FALLBACK_TO_STATIC) {
            return getBlogPostsByCategory(category)
          }
          throw error
        }
      }
      return getBlogPostsByCategory(category)
    },

    async getBySlug(slug: string): Promise<BlogPost | undefined> {
      if (USE_CMS_API) {
        try {
          return await blogApi.getBySlug(slug)
        } catch (error) {
          console.warn(`⚠️ CMS API failed for blog post ${slug}, falling back to static data:`, error)
          if (FALLBACK_TO_STATIC) {
            return getBlogPostBySlug(slug)
          }
          throw error
        }
      }
      return getBlogPostBySlug(slug)
    },

    async getCategories(): Promise<string[]> {
      if (USE_CMS_API) {
        try {
          return await cachedBlogApi.getCategories()
        } catch (error) {
          console.warn('⚠️ CMS API failed for blog categories, falling back to static data:', error)
          if (FALLBACK_TO_STATIC) {
            return getBlogCategories()
          }
          throw error
        }
      }
      return getBlogCategories()
    },

    async getAll(params?: {
      category?: string
      published?: boolean
      page?: number
      limit?: number
      search?: string
    }): Promise<{ posts: BlogPost[]; pagination?: any }> {
      if (USE_CMS_API) {
        try {
          return await blogApi.getAll(params)
        } catch (error) {
          console.warn('⚠️ CMS API failed for all blog posts, falling back to static data:', error)
          if (FALLBACK_TO_STATIC) {
            let posts = params?.category ? getBlogPostsByCategory(params.category) : getBlogPosts()
            
            if (params?.search) {
              const search = params.search.toLowerCase()
              posts = posts.filter(p => 
                p.title.toLowerCase().includes(search) ||
                p.excerpt.toLowerCase().includes(search) ||
                p.content.toLowerCase().includes(search)
              )
            }

            return { posts }
          }
          throw error
        }
      }
      
      // Static data implementation
      let posts = params?.category ? getBlogPostsByCategory(params.category) : getBlogPosts()
      
      if (params?.search) {
        const search = params.search.toLowerCase()
        posts = posts.filter(p => 
          p.title.toLowerCase().includes(search) ||
          p.excerpt.toLowerCase().includes(search) ||
          p.content.toLowerCase().includes(search)
        )
      }

      return { posts }
    },
  },

  // Testimonials
  testimonials: {
    async getFeatured(limit: number = 3): Promise<Testimonial[]> {
      if (USE_CMS_API) {
        try {
          return await cachedTestimonialsApi.getFeatured(limit)
        } catch (error) {
          console.warn('⚠️ CMS API failed for featured testimonials, falling back to static data:', error)
          if (FALLBACK_TO_STATIC) {
            return getFeaturedTestimonials(limit)
          }
          throw error
        }
      }
      return getFeaturedTestimonials(limit)
    },

    async getPublished(): Promise<Testimonial[]> {
      if (USE_CMS_API) {
        try {
          return await testimonialsApi.getPublished()
        } catch (error) {
          console.warn('⚠️ CMS API failed for published testimonials, falling back to static data:', error)
          if (FALLBACK_TO_STATIC) {
            return getTestimonials()
          }
          throw error
        }
      }
      return getTestimonials()
    },

    async getByCategory(category: string): Promise<Testimonial[]> {
      if (USE_CMS_API) {
        try {
          return await cachedTestimonialsApi.getByCategory(category)
        } catch (error) {
          console.warn(`⚠️ CMS API failed for testimonials category ${category}, falling back to static data:`, error)
          if (FALLBACK_TO_STATIC) {
            return getTestimonialsByCategory(category)
          }
          throw error
        }
      }
      return getTestimonialsByCategory(category)
    },

    async getById(id: string): Promise<Testimonial | undefined> {
      if (USE_CMS_API) {
        try {
          return await testimonialsApi.getById(id)
        } catch (error) {
          console.warn(`⚠️ CMS API failed for testimonial ${id}, falling back to static data:`, error)
          if (FALLBACK_TO_STATIC) {
            return getTestimonialById(id)
          }
          throw error
        }
      }
      return getTestimonialById(id)
    },

    async getAll(params?: {
      category?: string
      rating?: number
      published?: boolean
      page?: number
      limit?: number
      search?: string
    }): Promise<{ testimonials: Testimonial[]; pagination?: any }> {
      if (USE_CMS_API) {
        try {
          return await testimonialsApi.getAll(params)
        } catch (error) {
          console.warn('⚠️ CMS API failed for all testimonials, falling back to static data:', error)
          if (FALLBACK_TO_STATIC) {
            let testimonials = params?.category ? getTestimonialsByCategory(params.category) : getTestimonials()

            // Apply filters
            if (params?.rating !== undefined) {
              testimonials = testimonials.filter(t => t.rating === params.rating)
            }
            if (params?.search) {
              const search = params.search.toLowerCase()
              testimonials = testimonials.filter(t =>
                t.name.toLowerCase().includes(search) ||
                t.content.toLowerCase().includes(search) ||
                t.location?.toLowerCase().includes(search)
              )
            }

            return { testimonials }
          }
          throw error
        }
      }

      // Static data implementation
      let testimonials = params?.category ? getTestimonialsByCategory(params.category) : getTestimonials()

      if (params?.rating !== undefined) {
        testimonials = testimonials.filter(t => t.rating === params.rating)
      }
      if (params?.search) {
        const search = params.search.toLowerCase()
        testimonials = testimonials.filter(t =>
          t.name.toLowerCase().includes(search) ||
          t.content.toLowerCase().includes(search) ||
          t.location?.toLowerCase().includes(search)
        )
      }

      return { testimonials }
    },
  },
}

// Health check function
export const checkCmsConnection = async (): Promise<boolean> => {
  if (!USE_CMS_API) return false
  
  try {
    await productsApi.getAll({ limit: 1 })
    console.log('✅ CMS API connection successful')
    return true
  } catch (error) {
    console.warn('❌ CMS API connection failed:', error)
    return false
  }
}

// Initialize and check connection on module load
if (USE_CMS_API) {
  checkCmsConnection().then(connected => {
    if (connected) {
      console.log('🚀 Data Service initialized with CMS API')
    } else if (FALLBACK_TO_STATIC) {
      console.log('🔄 Data Service initialized with static data fallback')
    } else {
      console.error('💥 Data Service failed to initialize - no fallback enabled')
    }
  })
} else {
  console.log('📁 Data Service initialized with static data only')
}

export default dataService
