
import { Link } from "react-router-dom";
import { ShoppingBag, Heart } from "lucide-react";
import { Product } from "@/data/products";
import { formatPrice } from "@/lib/utils";

interface ProductCardProps {
  product: Product;
}

const ProductCard = ({ product }: ProductCardProps) => {
  return (
    <div className="product-card group">
      <div className="relative overflow-hidden">
        <Link to={`/products/${product.id}`}>
          <img
            src={product.images[0]}
            alt={`${product.name} - Premium ${product.category} by Dreamy Duffel - ${formatPrice(product.price)}`}
            className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-105"
          />
          {product.isCustomizable && (
            <div className="absolute top-2 right-2 bg-brand-gold text-white text-xs px-2 py-1 rounded">
              Customizable
            </div>
          )}
        </Link>
        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 p-2 translate-y-full group-hover:translate-y-0 transition-transform duration-300">
          <div className="flex justify-center space-x-4">
            <button className="text-white hover:text-brand-gold transition-colors">
              <ShoppingBag size={18} />
            </button>
            <button className="text-white hover:text-brand-gold transition-colors">
              <Heart size={18} />
            </button>
          </div>
        </div>
      </div>
      <div className="p-4">
        <h3 className="font-medium text-brand-charcoal">
          <Link to={`/products/${product.id}`} className="hover:text-brand-gold transition-colors">
            {product.name}
          </Link>
        </h3>
        <div className="mt-2 flex justify-between items-center">
          <span className="text-brand-gold font-semibold">{formatPrice(product.price)}</span>
          <span className="text-xs text-gray-500 capitalize">{product.category}</span>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
