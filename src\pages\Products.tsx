
import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import Layout from "@/components/layout/Layout";
import ProductCard from "@/components/product/ProductCard";
import { Product } from "@/data/products";
import { dataService } from "@/lib/dataService";
import { Filter, SlidersHorizontal } from "lucide-react";
import SEO from "@/components/seo/SEO";
import StructuredData from "@/components/seo/StructuredData";

const Products = () => {
  const [searchParams] = useSearchParams();
  const categoryParam = searchParams.get("category") || "all";

  const [products, setProducts] = useState<Product[]>([]);
  const [allProducts, setAllProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 20000]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([categoryParam]);
  const [sortOption, setSortOption] = useState("featured");

  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await dataService.products.getAll({
          category: categoryParam === "all" ? undefined : categoryParam
        });

        setAllProducts(response.products);
        setProducts(response.products);
        setSelectedCategories([categoryParam]);
      } catch (err) {
        console.error('Failed to load products:', err);
        setError('Failed to load products');
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, [categoryParam]);

  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  const handleCategoryChange = (category: string) => {
    if (category === "all") {
      setSelectedCategories(["all"]);
      setProducts(allProducts);
    } else {
      const newSelectedCategories = selectedCategories.includes("all")
        ? [category]
        : selectedCategories.includes(category)
          ? selectedCategories.filter(c => c !== category)
          : [...selectedCategories, category];

      setSelectedCategories(newSelectedCategories.length ? newSelectedCategories : ["all"]);

      const filteredProducts = newSelectedCategories.length
        ? allProducts.filter(product =>
          newSelectedCategories.includes(product.category) || newSelectedCategories.includes("all")
        )
        : allProducts;

      setProducts(filteredProducts);
    }
  };

  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const value = parseInt(e.target.value);
    const newPriceRange = [...priceRange] as [number, number];
    newPriceRange[index] = value;
    setPriceRange(newPriceRange);

    const filteredProducts = allProducts.filter(product => {
      const matchesCategory = selectedCategories.includes(product.category) || selectedCategories.includes("all");
      const matchesPrice = product.price >= newPriceRange[0] && product.price <= newPriceRange[1];
      return matchesCategory && matchesPrice;
    });

    setProducts(filteredProducts);
  };

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const option = e.target.value;
    setSortOption(option);

    const sortedProducts = [...products];
    switch (option) {
      case "price-low":
        sortedProducts.sort((a, b) => a.price - b.price);
        break;
      case "price-high":
        sortedProducts.sort((a, b) => b.price - a.price);
        break;
      case "name-asc":
        sortedProducts.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case "name-desc":
        sortedProducts.sort((a, b) => b.name.localeCompare(a.name));
        break;
      default:
        // Featured - no specific sort
        break;
    }

    setProducts(sortedProducts);
  };

  const getCategoryTitle = () => {
    switch (categoryParam) {
      case "handbags": return "Premium Handbags";
      case "caps": return "Stylish Caps";
      case "hairbands": return "Elegant Hair Bands";
      case "earrings": return "Beautiful Earrings";
      case "necklaces": return "Exquisite Necklaces";
      case "pillowcovers": return "Luxury Silk Pillow Covers";
      case "custom": return "Custom Designs";
      default: return "All Products";
    }
  };

  const getCategoryDescription = () => {
    switch (categoryParam) {
      case "handbags": return "Discover our collection of premium handbags crafted with attention to detail and style.";
      case "caps": return "Explore our range of stylish caps perfect for any occasion.";
      case "hairbands": return "Find elegant hair bands that add sophistication to your hairstyle.";
      case "earrings": return "Browse our beautiful collection of earrings for every occasion.";
      case "necklaces": return "Discover exquisite necklaces that complement your style perfectly.";
      case "pillowcovers": return "Experience luxury with our premium silk pillow covers for better sleep.";
      case "custom": return "Create personalized accessories with our custom design options.";
      default: return "Browse our complete collection of premium accessories and lifestyle products.";
    }
  };

  return (
    <Layout>
      <SEO
        title={`${getCategoryTitle()} | Dreamy Duffel`}
        description={`${getCategoryDescription()} Shop quality accessories in India with fast delivery and customization options.`}
        keywords={`${categoryParam === "all" ? "handbags, caps, accessories" : categoryParam}, premium accessories, Dreamy Duffel, India, ${categoryParam === "custom" ? "custom designs, personalized" : "quality craftsmanship"}`}
        url={`https://dreamyduffles.shop/products${categoryParam !== "all" ? `?category=${categoryParam}` : ""}`}
      />
      <StructuredData type="website" />
      <div className="bg-brand-cream py-10">
        <div className="container mx-auto px-4">
          <h1 className="font-serif text-3xl md:text-4xl font-bold text-brand-charcoal text-center mb-2">
            Our Products
          </h1>
          <p className="text-center text-gray-600 mb-8">
            Browse our collection of premium handbags and caps
          </p>

          <div className="flex flex-col md:flex-row gap-6">
            {/* Mobile Filter Toggle */}
            <button
              className="md:hidden flex items-center justify-center bg-white border border-gray-300 rounded-md px-4 py-2 mb-4 w-full"
              onClick={toggleFilters}
            >
              <Filter size={18} className="mr-2" />
              {showFilters ? "Hide Filters" : "Show Filters"}
            </button>

            {/* Filters Sidebar */}
            <div className={`${showFilters ? 'block' : 'hidden'} md:block md:w-1/4 bg-white p-6 rounded-lg shadow-sm`}>
              <div className="mb-6">
                <h3 className="font-serif text-lg font-bold text-brand-charcoal mb-3 flex items-center">
                  <SlidersHorizontal size={18} className="mr-2" />
                  Filters
                </h3>
                <div className="border-t border-gray-200 pt-4">
                  <h4 className="font-medium text-brand-charcoal mb-2">Categories</h4>
                  <div className="space-y-2">
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-brand-gold focus:ring-brand-gold"
                        checked={selectedCategories.includes("all")}
                        onChange={() => handleCategoryChange("all")}
                      />
                      <span className="ml-2 text-gray-600">All Products</span>
                    </label>
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-brand-gold focus:ring-brand-gold"
                        checked={selectedCategories.includes("handbags")}
                        onChange={() => handleCategoryChange("handbags")}
                      />
                      <span className="ml-2 text-gray-600">Handbags</span>
                    </label>
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-brand-gold focus:ring-brand-gold"
                        checked={selectedCategories.includes("caps")}
                        onChange={() => handleCategoryChange("caps")}
                      />
                      <span className="ml-2 text-gray-600">Caps</span>
                    </label>
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-brand-gold focus:ring-brand-gold"
                        checked={selectedCategories.includes("hairbands")}
                        onChange={() => handleCategoryChange("hairbands")}
                      />
                      <span className="ml-2 text-gray-600">Hair Bands</span>
                    </label>
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-brand-gold focus:ring-brand-gold"
                        checked={selectedCategories.includes("earrings")}
                        onChange={() => handleCategoryChange("earrings")}
                      />
                      <span className="ml-2 text-gray-600">Earrings</span>
                    </label>
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-brand-gold focus:ring-brand-gold"
                        checked={selectedCategories.includes("necklaces")}
                        onChange={() => handleCategoryChange("necklaces")}
                      />
                      <span className="ml-2 text-gray-600">Necklaces</span>
                    </label>
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-brand-gold focus:ring-brand-gold"
                        checked={selectedCategories.includes("pillowcovers")}
                        onChange={() => handleCategoryChange("pillowcovers")}
                      />
                      <span className="ml-2 text-gray-600">Silk Pillow Covers</span>
                    </label>
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-brand-gold focus:ring-brand-gold"
                        checked={selectedCategories.includes("custom")}
                        onChange={() => handleCategoryChange("custom")}
                      />
                      <span className="ml-2 text-gray-600">Custom Designs</span>
                    </label>
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <h4 className="font-medium text-brand-charcoal mb-2">Price Range</h4>
                <div className="flex justify-between mb-2">
                  <span className="text-sm text-gray-600">₹{priceRange[0]}</span>
                  <span className="text-sm text-gray-600">₹{priceRange[1]}</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="20000"
                  value={priceRange[0]}
                  onChange={(e) => handlePriceChange(e, 0)}
                  className="w-full"
                />
                <input
                  type="range"
                  min="0"
                  max="20000"
                  value={priceRange[1]}
                  onChange={(e) => handlePriceChange(e, 1)}
                  className="w-full"
                />
              </div>

              <div>
                <h4 className="font-medium text-brand-charcoal mb-2">Availability</h4>
                <label className="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-brand-gold focus:ring-brand-gold"
                    checked
                  />
                  <span className="ml-2 text-gray-600">In Stock</span>
                </label>
              </div>
            </div>

            {/* Products Grid */}
            <div className="md:w-3/4">
              <div className="bg-white p-4 rounded-lg shadow-sm mb-6 flex flex-col sm:flex-row justify-between items-center">
                <p className="text-gray-600 mb-3 sm:mb-0">
                  Showing <span className="font-medium">{products.length}</span> products
                </p>
                <div className="flex items-center">
                  <label className="text-gray-600 mr-2">Sort by:</label>
                  <select
                    value={sortOption}
                    onChange={handleSortChange}
                    className="border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-1 focus:ring-brand-gold"
                  >
                    <option value="featured">Featured</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="name-asc">Name: A to Z</option>
                    <option value="name-desc">Name: Z to A</option>
                  </select>
                </div>
              </div>

              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-gold"></div>
                  <span className="ml-3 text-gray-600">Loading products...</span>
                </div>
              ) : error ? (
                <div className="text-center py-12">
                  <p className="text-red-600 mb-4">{error}</p>
                  <button
                    onClick={() => window.location.reload()}
                    className="btn-primary"
                  >
                    Try Again
                  </button>
                </div>
              ) : products.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {products.map((product) => (
                    <ProductCard key={product.id} product={product} />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-600 mb-4">No products match your current filters.</p>
                  <button
                    onClick={() => {
                      setSelectedCategories(["all"]);
                      setPriceRange([0, 20000]);
                      setSortOption("featured");
                      setProducts(allProducts);
                    }}
                    className="btn-primary"
                  >
                    Reset Filters
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Products;
