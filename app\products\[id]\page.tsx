import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { dataService } from "@/lib/dataService";
import ProductDetailClient from './product-detail-client';

export async function generateStaticParams() {
  try {
    const response = await dataService.products.getAll();
    return response.products.map((product) => ({
      id: product.id,
    }));
  } catch (error) {
    console.error('Error generating static params for products:', error);
    return [];
  }
}

interface ProductDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export async function generateMetadata({ params }: ProductDetailPageProps): Promise<Metadata> {
  const { id } = await params;

  try {
    const product = await dataService.products.getById(id);
    
    if (!product) {
      return {
        title: 'Product Not Found | Dreamy Duffel',
        description: 'The product you are looking for could not be found.',
      };
    }

    return {
      title: `${product.name} | Dreamy Duffel`,
      description: product.description,
      keywords: `${product.name}, ${product.category}, premium accessories, Dreamy Duffel, Nepal`,
      openGraph: {
        title: `${product.name} | Dreamy Duffel`,
        description: product.description,
        url: `https://dreamyduffles.shop/products/${id}`,
        images: [
          {
            url: product.images[0],
            width: 800,
            height: 600,
            alt: product.name,
          },
        ],
        type: 'website',
      },
      twitter: {
        title: `${product.name} | Dreamy Duffel`,
        description: product.description,
        images: [product.images[0]],
      },
      alternates: {
        canonical: `https://dreamyduffles.shop/products/${id}`,
      },
    };
  } catch (error) {
    return {
      title: 'Product Not Found | Dreamy Duffel',
      description: 'The product you are looking for could not be found.',
    };
  }
}

export default async function ProductDetailPage({ params }: ProductDetailPageProps) {
  const { id } = await params;

  try {
    const product = await dataService.products.getById(id);

    if (!product) {
      notFound();
    }

    // Get related products
    const relatedProducts = await dataService.products.getByCategory(product.category);
    const filteredRelatedProducts = relatedProducts.filter(p => p.id !== id).slice(0, 4);

    return (
      <ProductDetailClient 
        product={product} 
        relatedProducts={filteredRelatedProducts}
      />
    );
  } catch (error) {
    console.error('Failed to load product:', error);
    notFound();
  }
}
