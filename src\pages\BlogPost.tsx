import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useState, useEffect } from "react";
import Layout from "@/components/layout/Layout";
import { BlogPost } from "@/data/blog";
import { dataService } from "@/lib/dataService";
import { Calendar, Clock, User, Tag, ArrowLeft } from "lucide-react";
import SEO from "@/components/seo/SEO";
import StructuredData from "@/components/seo/StructuredData";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";

const BlogPostPage = () => {
  const { slug } = useParams<{ slug: string }>();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadBlogPost = async () => {
      if (!slug) return;

      try {
        setLoading(true);
        setError(null);

        const fetchedPost = await dataService.blog.getBySlug(slug);
        if (fetchedPost) {
          setPost(fetchedPost);

          // Get related posts from the same category
          const related = await dataService.blog.getByCategory(fetchedPost.category);
          setRelatedPosts(related.filter(p => p.id !== fetchedPost.id).slice(0, 3));
        } else {
          setError('Blog post not found');
        }
      } catch (err) {
        console.error('Failed to load blog post:', err);
        setError('Failed to load blog post');
      } finally {
        setLoading(false);
      }
    };

    loadBlogPost();
  }, [slug]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-16 text-center">
          <div className="flex justify-center items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-gold"></div>
            <span className="ml-3 text-gray-600">Loading blog post...</span>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !post) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-16 text-center">
          <p className="text-xl text-red-600 mb-4">{error || 'Blog post not found'}</p>
          <Link to="/blog" className="btn-primary inline-block mt-4">
            Back to Blog
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <SEO 
        title={`${post.title} | Dreamy Duffel Blog`}
        description={post.excerpt}
        keywords={post.tags.join(', ')}
        url={`https://dreamyduffles.shop/blog/${post.slug}`}
        type="article"
        image={post.featuredImage}
        article={{
          publishedTime: post.publishedAt,
          modifiedTime: post.updatedAt,
          author: post.author,
          section: post.category,
          tags: post.tags
        }}
      />
      <StructuredData 
        type="breadcrumb" 
        breadcrumbs={[
          { name: "Home", url: "https://dreamyduffles.shop/" },
          { name: "Blog", url: "https://dreamyduffles.shop/blog" },
          { name: post.title, url: `https://dreamyduffles.shop/blog/${post.slug}` }
        ]}
      />

      <div className="container mx-auto px-4 py-12">
        {/* Breadcrumbs */}
        <div className="mb-8">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link to="/">Home</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link to="/blog">Blog</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>{post.title}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        {/* Back to Blog */}
        <Link 
          to="/blog" 
          className="inline-flex items-center text-brand-gold hover:text-brand-charcoal transition-colors mb-8"
        >
          <ArrowLeft size={16} className="mr-2" />
          Back to Blog
        </Link>

        <article className="max-w-4xl mx-auto">
          {/* Featured Image */}
          <img
            src={post.featuredImage}
            alt={`${post.title} - Dreamy Duffel Blog`}
            className="w-full h-64 md:h-96 object-cover rounded-lg mb-8"
          />

          {/* Post Header */}
          <header className="mb-8">
            <div className="flex items-center text-sm text-gray-500 mb-4">
              <Tag size={14} className="mr-1" />
              <span className="mr-4">{post.category}</span>
              <Clock size={14} className="mr-1" />
              <span>{post.readTime} min read</span>
            </div>

            <h1 className="font-serif text-3xl md:text-4xl font-bold text-brand-charcoal mb-4">
              {post.title}
            </h1>

            <div className="flex items-center justify-between text-sm text-gray-500 border-b border-gray-200 pb-4">
              <div className="flex items-center">
                <User size={14} className="mr-1" />
                <span>{post.author}</span>
              </div>
              <div className="flex items-center">
                <Calendar size={14} className="mr-1" />
                <span>{formatDate(post.publishedAt)}</span>
              </div>
            </div>
          </header>

          {/* Post Content */}
          <div 
            className="prose prose-lg max-w-none mb-8"
            dangerouslySetInnerHTML={{ __html: post.content }}
          />

          {/* Tags */}
          <div className="border-t border-gray-200 pt-6 mb-8">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Tags:</h3>
            <div className="flex flex-wrap gap-2">
              {post.tags.map((tag, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-full"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </article>

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <div className="mt-16">
            <h2 className="font-serif text-2xl font-bold text-brand-charcoal mb-8">
              Related Articles
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedPosts.map((relatedPost) => (
                <article key={relatedPost.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                  <Link to={`/blog/${relatedPost.slug}`}>
                    <img
                      src={relatedPost.featuredImage}
                      alt={`${relatedPost.title} - Dreamy Duffel Blog`}
                      className="w-full h-32 object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </Link>
                  <div className="p-4">
                    <h3 className="font-medium text-brand-charcoal mb-2 hover:text-brand-gold transition-colors">
                      <Link to={`/blog/${relatedPost.slug}`}>
                        {relatedPost.title}
                      </Link>
                    </h3>
                    <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                      {relatedPost.excerpt}
                    </p>
                    <div className="text-xs text-gray-500">
                      {formatDate(relatedPost.publishedAt)}
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default BlogPostPage;
