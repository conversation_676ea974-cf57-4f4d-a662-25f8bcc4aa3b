
import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import Layout from "@/components/layout/Layout";
import { Product } from "@/data/products";
import { dataService } from "@/lib/dataService";
import { ShoppingBag, Heart, ChevronRight, ChevronLeft, Check, MinusCircle, PlusCircle } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ProductCard from "@/components/product/ProductCard";
import { formatPrice } from "@/lib/utils";
import SEO from "@/components/seo/SEO";
import StructuredData from "@/components/seo/StructuredData";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";

const ProductDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [product, setProduct] = useState<Product | null>(null);
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [mainImage, setMainImage] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [customText, setCustomText] = useState("");

  useEffect(() => {
    const loadProduct = async () => {
      if (!id) return;

      try {
        setLoading(true);
        setError(null);

        const fetchedProduct = await dataService.products.getById(id);
        if (fetchedProduct) {
          setProduct(fetchedProduct);
          setMainImage(fetchedProduct.images[0]);

          // Get related products
          const related = await dataService.products.getByCategory(fetchedProduct.category);
          setRelatedProducts(related.filter(p => p.id !== id).slice(0, 4));
        } else {
          setError('Product not found');
        }
      } catch (err) {
        console.error('Failed to load product:', err);
        setError('Failed to load product');
      } finally {
        setLoading(false);
      }
    };

    loadProduct();
  }, [id]);

  const handleImageChange = (image: string) => {
    setMainImage(image);
  };

  const incrementQuantity = () => {
    setQuantity(prev => prev + 1);
  };

  const decrementQuantity = () => {
    setQuantity(prev => (prev > 1 ? prev - 1 : 1));
  };

  if (loading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-16 text-center">
          <div className="flex justify-center items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-gold"></div>
            <span className="ml-3 text-gray-600">Loading product...</span>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !product) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-16 text-center">
          <p className="text-xl text-red-600 mb-4">{error || 'Product not found'}</p>
          <Link to="/products" className="btn-primary inline-block mt-4">
            Back to Products
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <SEO
        title={`${product.name} - Premium ${product.category} | Dreamy Duffel`}
        description={`${product.description} Shop ${product.name} at Dreamy Duffel. ${product.isCustomizable ? 'Customizable design available.' : ''} Price: ${formatPrice(product.price)}`}
        keywords={`${product.name}, ${product.category}, ${product.isCustomizable ? 'custom ' : ''}${product.category}, Dreamy Duffel, premium accessories, India`}
        url={`https://dreamyduffles.shop/products/${product.id}`}
        type="product"
        image={product.images[0]}
        product={{
          price: product.price,
          currency: "INR",
          availability: product.inStock ? "in stock" : "out of stock",
          condition: "new",
          brand: "Dreamy Duffel",
          category: product.category
        }}
      />
      <StructuredData
        type="product"
        product={product}
      />
      <StructuredData
        type="breadcrumb"
        breadcrumbs={[
          { name: "Home", url: "https://dreamyduffles.shop/" },
          { name: "Products", url: "https://dreamyduffles.shop/products" },
          { name: product.name, url: `https://dreamyduffles.shop/products/${product.id}` }
        ]}
      />
      <div className="container mx-auto px-4 py-12">
        {/* Breadcrumbs */}
        <div className="mb-8">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link to="/">Home</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link to="/products">Products</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>{product.name}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          {/* Product Images */}
          <div>
            <div className="mb-4 bg-white rounded-lg overflow-hidden shadow-md">
              <img
                src={mainImage}
                alt={`${product.name} - Premium ${product.category} by Dreamy Duffel`}
                className="w-full h-auto object-cover"
              />
            </div>
            <div className="flex space-x-2">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => handleImageChange(image)}
                  className={`border-2 rounded-md overflow-hidden ${mainImage === image ? 'border-brand-gold' : 'border-transparent'
                    }`}
                >
                  <img
                    src={image}
                    alt={`${product.name} - View ${index + 1} - Premium ${product.category} by Dreamy Duffel`}
                    className="w-20 h-20 object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div>
            <h1 className="font-serif text-3xl font-bold text-brand-charcoal mb-2">
              {product.name}
            </h1>
            <div className="flex items-center mb-4">
              <div className="flex text-yellow-400 mr-2">
                <span>★</span><span>★</span><span>★</span><span>★</span><span>★</span>
              </div>
              <span className="text-gray-600 text-sm">(24 reviews)</span>
            </div>

            <div className="text-2xl font-bold text-brand-gold mb-6">
              {formatPrice(product.price)}
            </div>

            <div className="mb-6">
              <p className="text-gray-600 leading-relaxed mb-4">
                {product.description}
              </p>
              <ul className="space-y-2">
                {product.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <Check size={18} className="text-brand-gold mr-2 mt-0.5" />
                    <span className="text-gray-600">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Customization Option */}
            {product.isCustomizable && (
              <div className="mb-6 p-4 bg-brand-cream rounded-lg">
                <h3 className="font-medium text-brand-charcoal mb-2">Customize Your Product</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Add your personal touch with custom text, initials, or a short message.
                </p>
                <input
                  type="text"
                  value={customText}
                  onChange={(e) => setCustomText(e.target.value)}
                  placeholder="Enter your custom text"
                  maxLength={20}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-gold"
                />
                {customText && (
                  <p className="text-xs text-gray-500 mt-1">
                    Preview: "{customText}"
                  </p>
                )}
              </div>
            )}

            {/* Quantity Selector */}
            <div className="flex items-center mb-6">
              <span className="text-gray-600 mr-4">Quantity:</span>
              <div className="flex items-center border border-gray-300 rounded-md">
                <button
                  onClick={decrementQuantity}
                  className="px-3 py-1 text-gray-600 hover:text-brand-gold"
                >
                  <MinusCircle size={18} />
                </button>
                <span className="px-4 py-1 text-center border-x border-gray-300 min-w-[40px]">
                  {quantity}
                </span>
                <button
                  onClick={incrementQuantity}
                  className="px-3 py-1 text-gray-600 hover:text-brand-gold"
                >
                  <PlusCircle size={18} />
                </button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 mb-8">
              <button className="btn-primary flex items-center justify-center flex-1">
                <ShoppingBag size={18} className="mr-2" />
                Add to Cart
              </button>
              <button className="btn-outline flex items-center justify-center">
                <Heart size={18} className="mr-2" />
                Wishlist
              </button>
            </div>

            {/* Additional Info */}
            <div className="border-t border-gray-200 pt-4">
              <div className="flex items-center mb-1">
                <span className="font-medium text-brand-charcoal w-28">Availability:</span>
                <span className={`ml-2 ${product.inStock ? 'text-green-600' : 'text-red-600'}`}>
                  {product.inStock ? 'In Stock' : 'Out of Stock'}
                </span>
              </div>
              <div className="flex items-center mb-1">
                <span className="font-medium text-brand-charcoal w-28">Category:</span>
                <span className="ml-2 text-gray-600 capitalize">{product.category}</span>
              </div>
              <div className="flex items-center">
                <span className="font-medium text-brand-charcoal w-28">Share:</span>
                <div className="flex space-x-2 ml-2">
                  <a href="#" className="text-gray-600 hover:text-brand-gold">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                    </svg>
                  </a>
                  <a href="#" className="text-gray-600 hover:text-brand-gold">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
                    </svg>
                  </a>
                  <a href="#" className="text-gray-600 hover:text-brand-gold">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                      <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                      <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mt-16">
          <Tabs defaultValue="description">
            <TabsList className="w-full border-b border-gray-200 mb-6">
              <TabsTrigger value="description" className="text-lg font-medium">Description</TabsTrigger>
              <TabsTrigger value="details" className="text-lg font-medium">Additional Details</TabsTrigger>
              <TabsTrigger value="reviews" className="text-lg font-medium">Reviews (24)</TabsTrigger>
            </TabsList>
            <TabsContent value="description" className="text-gray-600 leading-relaxed">
              <p className="mb-4">{product.description}</p>
              <p>Our {product.name} is crafted with attention to detail, ensuring both style and durability. Each piece undergoes rigorous quality control to meet our exacting standards.</p>
              {product.isCustomizable && (
                <div className="mt-4 p-4 bg-brand-cream rounded-lg">
                  <h3 className="font-medium text-brand-charcoal mb-2">Customization Details</h3>
                  <p>
                    This product can be personalized with your choice of text, initials, or a short message. Our skilled artisans will carefully add your custom text using premium techniques that ensure longevity. Please allow an additional 2-3 business days for customized items.
                  </p>
                </div>
              )}
            </TabsContent>
            <TabsContent value="details">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="font-medium text-brand-charcoal mb-3">Specifications</h3>
                  <table className="min-w-full">
                    <tbody className="divide-y divide-gray-200">
                      {product.features.map((feature, index) => {
                        const [key, value] = feature.includes(':')
                          ? feature.split(':')
                          : [feature, ''];
                        return (
                          <tr key={index}>
                            <td className="py-2 text-gray-600">{key}</td>
                            <td className="py-2 text-gray-600">{value}</td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
                <div>
                  <h3 className="font-medium text-brand-charcoal mb-3">Care Instructions</h3>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-start">
                      <Check size={18} className="text-brand-gold mr-2 mt-0.5" />
                      <span>Clean with a soft, dry cloth</span>
                    </li>
                    <li className="flex items-start">
                      <Check size={18} className="text-brand-gold mr-2 mt-0.5" />
                      <span>Avoid exposure to excessive moisture</span>
                    </li>
                    <li className="flex items-start">
                      <Check size={18} className="text-brand-gold mr-2 mt-0.5" />
                      <span>Store in a cool, dry place when not in use</span>
                    </li>
                    <li className="flex items-start">
                      <Check size={18} className="text-brand-gold mr-2 mt-0.5" />
                      <span>Do not use harsh chemicals or solvents</span>
                    </li>
                  </ul>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="reviews">
              <div className="space-y-6">
                <div className="flex flex-col md:flex-row md:items-center justify-between p-6 bg-brand-cream rounded-lg">
                  <div className="mb-6 md:mb-0">
                    <h3 className="font-medium text-brand-charcoal text-lg mb-2">Customer Reviews</h3>
                    <div className="flex items-center">
                      <div className="flex text-yellow-400 mr-2">
                        <span>★</span><span>★</span><span>★</span><span>★</span><span>★</span>
                      </div>
                      <span className="text-gray-600">Based on 24 reviews</span>
                    </div>
                  </div>
                  <button className="btn-primary">Write a Review</button>
                </div>

                {/* Sample Reviews */}
                <div className="border-b border-gray-200 pb-6">
                  <div className="flex justify-between mb-2">
                    <h4 className="font-medium text-brand-charcoal">Emma T.</h4>
                    <span className="text-gray-500 text-sm">2 weeks ago</span>
                  </div>
                  <div className="flex text-yellow-400 mb-2">
                    <span>★</span><span>★</span><span>★</span><span>★</span><span>★</span>
                  </div>
                  <p className="text-gray-600">
                    This product exceeded my expectations! The quality is exceptional and it looks even better in person than in the photos. Highly recommend!
                  </p>
                </div>

                <div className="border-b border-gray-200 pb-6">
                  <div className="flex justify-between mb-2">
                    <h4 className="font-medium text-brand-charcoal">James R.</h4>
                    <span className="text-gray-500 text-sm">1 month ago</span>
                  </div>
                  <div className="flex text-yellow-400 mb-2">
                    <span>★</span><span>★</span><span>★</span><span>★</span><span>☆</span>
                  </div>
                  <p className="text-gray-600">
                    Great product overall. The design is beautiful and the materials feel premium. The only minor issue was that shipping took a bit longer than expected, but it was worth the wait.
                  </p>
                </div>

                <div>
                  <div className="flex justify-between mb-2">
                    <h4 className="font-medium text-brand-charcoal">Sophie L.</h4>
                    <span className="text-gray-500 text-sm">2 months ago</span>
                  </div>
                  <div className="flex text-yellow-400 mb-2">
                    <span>★</span><span>★</span><span>★</span><span>★</span><span>★</span>
                  </div>
                  <p className="text-gray-600">
                    I bought this as a gift for my sister and she absolutely loves it! The customization option made it extra special. The customer service was also excellent when I had questions about the order.
                  </p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <div className="mt-16">
            <h2 className="font-serif text-2xl font-bold text-brand-charcoal mb-8">
              You May Also Like
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default ProductDetail;
